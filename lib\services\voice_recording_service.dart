import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:record/record.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:audioplayers/audioplayers.dart';

class VoiceRecordingService {
  static final VoiceRecordingService _instance = VoiceRecordingService._internal();
  factory VoiceRecordingService() => _instance;
  VoiceRecordingService._internal();

  final AudioRecorder _recorder = AudioRecorder();
  final AudioPlayer _player = AudioPlayer();
  
  bool _isRecording = false;
  bool _isPlaying = false;
  String? _currentRecordingPath;
  Duration _recordingDuration = Duration.zero;
  
  // Getters
  bool get isRecording => _isRecording;
  bool get isPlaying => _isPlaying;
  Duration get recordingDuration => _recordingDuration;
  String? get currentRecordingPath => _currentRecordingPath;

  // Stream controllers للاستماع للتغييرات
  final ValueNotifier<bool> isRecordingNotifier = ValueNotifier(false);
  final ValueNotifier<bool> isPlayingNotifier = ValueNotifier(false);
  final ValueNotifier<Duration> recordingDurationNotifier = ValueNotifier(Duration.zero);

  /// طلب إذن الميكروفون
  Future<bool> requestMicrophonePermission() async {
    final status = await Permission.microphone.request();
    return status == PermissionStatus.granted;
  }

  /// بدء التسجيل
  Future<bool> startRecording() async {
    try {
      // التحقق من الإذن
      if (!await requestMicrophonePermission()) {
        if (kDebugMode) print('إذن الميكروفون مرفوض');
        return false;
      }

      // إنشاء مسار الملف
      final directory = await getTemporaryDirectory();
      final fileName = 'voice_reaction_${DateTime.now().millisecondsSinceEpoch}.m4a';
      _currentRecordingPath = '${directory.path}/$fileName';

      // بدء التسجيل
      await _recorder.start(
        const RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        ),
        path: _currentRecordingPath!,
      );

      _isRecording = true;
      _recordingDuration = Duration.zero;
      
      // تحديث المستمعين
      isRecordingNotifier.value = true;
      recordingDurationNotifier.value = Duration.zero;

      // بدء عداد الوقت
      _startDurationTimer();

      if (kDebugMode) print('بدء التسجيل: $_currentRecordingPath');
      return true;
    } catch (e) {
      if (kDebugMode) print('خطأ في بدء التسجيل: $e');
      return false;
    }
  }

  /// إيقاف التسجيل
  Future<String?> stopRecording() async {
    try {
      if (!_isRecording) return null;

      final path = await _recorder.stop();
      _isRecording = false;
      
      // تحديث المستمعين
      isRecordingNotifier.value = false;

      if (kDebugMode) print('تم إيقاف التسجيل: $path');
      return path;
    } catch (e) {
      if (kDebugMode) print('خطأ في إيقاف التسجيل: $e');
      return null;
    }
  }

  /// إلغاء التسجيل
  Future<void> cancelRecording() async {
    try {
      if (_isRecording) {
        await _recorder.stop();
        _isRecording = false;
        isRecordingNotifier.value = false;
      }

      // حذف الملف إذا كان موجوداً
      if (_currentRecordingPath != null) {
        final file = File(_currentRecordingPath!);
        if (await file.exists()) {
          await file.delete();
        }
        _currentRecordingPath = null;
      }

      _recordingDuration = Duration.zero;
      recordingDurationNotifier.value = Duration.zero;

      if (kDebugMode) print('تم إلغاء التسجيل');
    } catch (e) {
      if (kDebugMode) print('خطأ في إلغاء التسجيل: $e');
    }
  }

  /// تشغيل التسجيل
  Future<bool> playRecording(String filePath) async {
    try {
      if (_isPlaying) {
        await stopPlaying();
      }

      await _player.play(DeviceFileSource(filePath));
      _isPlaying = true;
      isPlayingNotifier.value = true;

      // الاستماع لانتهاء التشغيل
      _player.onPlayerComplete.listen((_) {
        _isPlaying = false;
        isPlayingNotifier.value = false;
      });

      if (kDebugMode) print('بدء تشغيل التسجيل: $filePath');
      return true;
    } catch (e) {
      if (kDebugMode) print('خطأ في تشغيل التسجيل: $e');
      return false;
    }
  }

  /// إيقاف التشغيل
  Future<void> stopPlaying() async {
    try {
      await _player.stop();
      _isPlaying = false;
      isPlayingNotifier.value = false;
      if (kDebugMode) print('تم إيقاف التشغيل');
    } catch (e) {
      if (kDebugMode) print('خطأ في إيقاف التشغيل: $e');
    }
  }

  /// بدء عداد مدة التسجيل
  void _startDurationTimer() {
    Future.doWhile(() async {
      if (!_isRecording) return false;
      
      await Future.delayed(const Duration(seconds: 1));
      if (_isRecording) {
        _recordingDuration = Duration(seconds: _recordingDuration.inSeconds + 1);
        recordingDurationNotifier.value = _recordingDuration;
      }
      
      return _isRecording;
    });
  }

  /// الحصول على مدة الملف الصوتي
  Future<Duration?> getAudioDuration(String filePath) async {
    try {
      await _player.setSource(DeviceFileSource(filePath));
      return await _player.getDuration();
    } catch (e) {
      if (kDebugMode) print('خطأ في الحصول على مدة الملف: $e');
      return null;
    }
  }

  /// تنظيف الموارد
  void dispose() {
    _recorder.dispose();
    _player.dispose();
    isRecordingNotifier.dispose();
    isPlayingNotifier.dispose();
    recordingDurationNotifier.dispose();
  }
}
