-- جدول التفاعلات الصوتية
CREATE TABLE voice_reactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    audio_url TEXT NOT NULL, -- رابط الملف الصوتي في Supabase Storage
    audio_duration INTEGER, -- مدة التسجيل بالثواني
    transcription TEXT, -- النص المحول من الصوت (اختياري)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- فهرس مركب لتجنب التكرار
    UNIQUE(post_id, user_id)
);

-- فهارس لتحسين الأداء
CREATE INDEX idx_voice_reactions_post_id ON voice_reactions(post_id);
CREATE INDEX idx_voice_reactions_user_id ON voice_reactions(user_id);
CREATE INDEX idx_voice_reactions_created_at ON voice_reactions(created_at DESC);

-- تحديث timestamp عند التعديل
CREATE OR REPLACE FUNCTION update_voice_reactions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_voice_reactions_updated_at
    BEFORE UPDATE ON voice_reactions
    FOR EACH ROW
    EXECUTE FUNCTION update_voice_reactions_updated_at();

-- سياسات الأمان (RLS)
ALTER TABLE voice_reactions ENABLE ROW LEVEL SECURITY;

-- السماح للمستخدمين بقراءة جميع التفاعلات الصوتية
CREATE POLICY "Users can view all voice reactions" ON voice_reactions
    FOR SELECT USING (true);

-- السماح للمستخدمين بإنشاء تفاعلاتهم الصوتية فقط
CREATE POLICY "Users can insert their own voice reactions" ON voice_reactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- السماح للمستخدمين بتحديث تفاعلاتهم الصوتية فقط
CREATE POLICY "Users can update their own voice reactions" ON voice_reactions
    FOR UPDATE USING (auth.uid() = user_id);

-- السماح للمستخدمين بحذف تفاعلاتهم الصوتية فقط
CREATE POLICY "Users can delete their own voice reactions" ON voice_reactions
    FOR DELETE USING (auth.uid() = user_id);

-- إنشاء bucket للملفات الصوتية في Storage
INSERT INTO storage.buckets (id, name, public) 
VALUES ('voice-reactions', 'voice-reactions', true);

-- سياسات Storage للملفات الصوتية
CREATE POLICY "Users can upload voice reactions" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'voice-reactions' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can view voice reactions" ON storage.objects
    FOR SELECT USING (bucket_id = 'voice-reactions');

CREATE POLICY "Users can update their voice reactions" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'voice-reactions' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete their voice reactions" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'voice-reactions' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- دالة للحصول على عدد التفاعلات الصوتية لكل منشور
CREATE OR REPLACE FUNCTION get_voice_reactions_count(post_uuid UUID)
RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)::INTEGER 
        FROM voice_reactions 
        WHERE post_id = post_uuid
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة للحصول على التفاعل الصوتي للمستخدم الحالي
CREATE OR REPLACE FUNCTION get_user_voice_reaction(post_uuid UUID)
RETURNS TABLE(
    id UUID,
    audio_url TEXT,
    audio_duration INTEGER,
    transcription TEXT,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        vr.id,
        vr.audio_url,
        vr.audio_duration,
        vr.transcription,
        vr.created_at
    FROM voice_reactions vr
    WHERE vr.post_id = post_uuid AND vr.user_id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
