class VoiceReaction {
  final String id;
  final String postId;
  final String userId;
  final String audioUrl;
  final int? audioDuration; // بالثواني
  final String? transcription;
  final DateTime createdAt;
  final DateTime updatedAt;

  const VoiceReaction({
    required this.id,
    required this.postId,
    required this.userId,
    required this.audioUrl,
    this.audioDuration,
    this.transcription,
    required this.createdAt,
    required this.updatedAt,
  });

  factory VoiceReaction.fromJson(Map<String, dynamic> json) {
    return VoiceReaction(
      id: json['id'] as String,
      postId: json['post_id'] as String,
      userId: json['user_id'] as String,
      audioUrl: json['audio_url'] as String,
      audioDuration: json['audio_duration'] as int?,
      transcription: json['transcription'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'post_id': postId,
      'user_id': userId,
      'audio_url': audioUrl,
      'audio_duration': audioDuration,
      'transcription': transcription,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  VoiceReaction copyWith({
    String? id,
    String? postId,
    String? userId,
    String? audioUrl,
    int? audioDuration,
    String? transcription,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return VoiceReaction(
      id: id ?? this.id,
      postId: postId ?? this.postId,
      userId: userId ?? this.userId,
      audioUrl: audioUrl ?? this.audioUrl,
      audioDuration: audioDuration ?? this.audioDuration,
      transcription: transcription ?? this.transcription,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VoiceReaction && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'VoiceReaction(id: $id, postId: $postId, userId: $userId, audioUrl: $audioUrl, audioDuration: $audioDuration)';
  }
}
