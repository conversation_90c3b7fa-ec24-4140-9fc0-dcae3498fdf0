# اختبار التفاعلات الصوتية

## الخطوات للاختبار:

### 1. اختبار الضغط المطول على زر الإعجاب:
- افتح التطبيق
- اذهب إلى أي منشور
- اضغط مطولاً على زر الإعجاب
- يجب أن تظهر 7 أيقونات تفاعل + أيقونة ميكروفون حمراء
- يجب أن تظهر الأيقونات بتأثير متتالي (واحدة تلو الأخرى)

### 2. اختبار النقر العادي على زر الإعجاب:
- اضغط نقرة عادية على زر الإعجاب
- إذا لم يكن هناك تفاعل سابق، يجب أن يضع إعجاب
- إذا كان هناك تفاعل آخر، يجب أن يفتح modal bottom sheet مع الأيقونات

### 3. اختبار أيقونة الميكروفون:
- اضغط على أيقونة الميكروفون الحمراء
- يجب أن يفتح حوار التفاعل الصوتي
- اضغط على أيقونة الميكروفون في الحوار
- يجب أن يبدأ التسجيل مع تأثير نبضي
- تحدث لبضع ثوان
- اضغط زر الإيقاف
- يجب أن يتم رفع التسجيل وحفظه

### 4. المشاكل المحتملة:
- إذا لم تظهر الأيقونات: تحقق من أن عدد animation controllers = 8
- إذا لم يعمل التسجيل: تحقق من إذن الميكروفون
- إذا لم يتم الرفع: تحقق من إعدادات Supabase Storage

### 5. قاعدة البيانات:
تأكد من تشغيل SQL في Supabase:
```sql
-- جدول التفاعلات الصوتية
CREATE TABLE voice_reactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES posts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    audio_url TEXT NOT NULL,
    audio_duration INTEGER,
    transcription TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(post_id, user_id)
);

-- إنشاء bucket للملفات الصوتية
INSERT INTO storage.buckets (id, name, public) 
VALUES ('voice-reactions', 'voice-reactions', true);
```
