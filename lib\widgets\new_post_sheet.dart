import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:metadata_fetch/metadata_fetch.dart';
import 'package:path/path.dart' as p;
import 'package:flutter/cupertino.dart';
import 'package:file_picker/file_picker.dart';
import 'package:record/record.dart' show AudioRecorder, RecordConfig, AudioEncoder;
import 'package:path_provider/path_provider.dart';
import 'dart:async';

import '../supabase_service.dart';
import '../models/post.dart';
import '../pages/live_stream_setup_page.dart';

class NewPostSheet extends StatefulWidget {
  const NewPostSheet({super.key});

  @override
  State<NewPostSheet> createState() => _NewPostSheetState();
}

class _NewPostSheetState extends State<NewPostSheet> {
  final TextEditingController _textController = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  final AudioRecorder _recorder = AudioRecorder();
  bool _isRecording = false;

  XFile? _selectedMedia;
  PostType? _mediaType; // image أو video

  String? _linkUrl;
  Map<String, dynamic>? _linkMeta;

  String? _bgColor; // hex like #RRGGBB
  Color? _bgColorColor; // real color for convenience

  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
        top: 20,
        left: 20,
        right: 20,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _textController,
            maxLines: 5,
            decoration: InputDecoration(
              hintText: 'ماذا يدور في ذهنك؟',
              filled: true,
              fillColor: _bgColorColor,
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            ),
            style: TextStyle(
              color: _bgColorColor != null
                  ? _chooseTextColor(_bgColorColor!)
                  : Colors.black,
              fontSize: _bgColorColor != null ? 24 : 16,
              fontWeight: _bgColorColor != null ? FontWeight.bold : FontWeight.normal,
            ),
            textAlign: _bgColorColor != null ? TextAlign.center : TextAlign.start,
            textDirection: TextDirection.rtl,
          ),
          if (_selectedMedia != null) _buildMediaPreview(),
          if (_linkMeta != null) _buildLinkPreview(),
          SizedBox(
            height: 170,
            child: GridView.count(
              crossAxisCount: 4,
              mainAxisSpacing: 12,
              crossAxisSpacing: 12,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _ActionCircle(icon: CupertinoIcons.photo, label: 'صورة', onTap: _pickImage),
                _ActionCircle(icon: CupertinoIcons.videocam, label: 'فيديو', onTap: _pickVideo),
                _ActionCircle(icon: CupertinoIcons.music_note_2, label: 'مقطع صوتي', onTap: _pickAudio),
                _ActionCircle(icon: CupertinoIcons.link, label: 'رابط', onTap: _addLink),
                _ActionCircle(icon: CupertinoIcons.paintbrush, label: 'لون', onTap: _pickColor),
                _VoiceActionCircle(onSend: _recordAndSendVoice),
                _ActionCircle(icon: CupertinoIcons.dot_radiowaves_left_right, label: 'بث مباشر', onTap: _startLiveStream),
              ],
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            height: 50,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[800],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              onPressed: _isLoading ? null : _submit,
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text('نشر', style: TextStyle(fontWeight: FontWeight.bold)),
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildMediaPreview() {
    if (_selectedMedia == null) return const SizedBox.shrink();
    if (_mediaType == PostType.image) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: Image.file(File(_selectedMedia!.path), height: 150, fit: BoxFit.cover),
      );
    } else if (_mediaType == PostType.audio) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: Container(
          height: 80,
          decoration: BoxDecoration(color: Colors.blue.shade50, borderRadius: BorderRadius.circular(12)),
          child: Center(child: Text('ملف صوتى مرفق: ${p.basename(_selectedMedia!.path)}')),
        ),
      );
    } else {
      // فيديو: إظهار أيقونة فيديو فقط
      return Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: Container(
          height: 150,
          color: Colors.black12,
          child: const Center(
            child: Icon(Icons.videocam, size: 64, color: Colors.red),
          ),
        ),
      );
    }
  }

  Widget _buildLinkPreview() {
    final title = _linkMeta?['title'] ?? _linkUrl;
    final image = _linkMeta?['image'];
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: image != null
            ? Image.network(image, width: 50, height: 50, fit: BoxFit.cover)
            : const Icon(Icons.link, color: Colors.red),
        title: Text(title ?? ''),
        subtitle: Text(_linkUrl ?? ''),
      ),
    );
  }

  Future<void> _pickImage() async {
    final image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        _selectedMedia = image;
        _mediaType = PostType.image;
      });
    }
  }

  Future<void> _pickVideo() async {
    final video = await _picker.pickVideo(source: ImageSource.gallery);
    if (video != null) {
      setState(() {
        _selectedMedia = video;
        _mediaType = PostType.video;
      });
    }
  }

  Future<void> _pickAudio() async {
    final res = await FilePicker.platform.pickFiles(type: FileType.audio);
    if (res != null && res.files.isNotEmpty) {
      final file = res.files.first;
      setState(() {
        _selectedMedia = XFile(file.path!);
        _mediaType = PostType.audio;
      });
    }
  }

  Future<void> _addLink() async {
    final controller = TextEditingController();
    await showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('أدخل الرابط'),
          content: TextField(
            controller: controller,
            decoration: const InputDecoration(hintText: 'https://example.com'),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context, controller.text.trim());
              },
              child: const Text('تم'),
            ),
          ],
        );
      },
    ).then((value) async {
      if (value != null && value is String && value.isNotEmpty) {
        setState(() {
          _linkUrl = value;
        });
        final meta = await MetadataFetch.extract(value);
        setState(() {
          _linkMeta = {
            'title': meta?.title,
            'description': meta?.description,
            'image': meta?.image,
          };
        });
      }
    });
  }

  Future<void> _pickColor() async {
    final colors = [
      Colors.red,
      Colors.green,
      Colors.blue,
      Colors.orange,
      Colors.purple,
      Colors.brown,
      Colors.teal,
      Colors.pink,
      Colors.amber,
      Colors.indigo,
    ];
    final selected = await showModalBottomSheet<Color>(
      context: context,
      builder: (ctx) => SizedBox(
        height: 200,
        child: GridView.count(
          crossAxisCount: 5,
          children: colors
              .map((c) => GestureDetector(
                    onTap: () => Navigator.pop(ctx, c),
                    child: Container(
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(color: c, shape: BoxShape.circle),
                    ),
                  ))
              .toList(),
        ),
      ),
    );
    if (selected != null) {
      setState(() {
        _bgColorColor = selected;
        _bgColor = '#${selected.value.toRadixString(16).substring(2).toUpperCase()}';
        _selectedMedia = null;
        _linkUrl = null;
        _linkMeta = null;
      });
    }
  }

  Color _chooseTextColor(Color background) {
    final brightness = ThemeData.estimateBrightnessForColor(background);
    // If background is light, return black; else white
    return brightness == Brightness.light ? Colors.black : Colors.white;
  }

  void _startLiveStream() {
    Navigator.pop(context); // أغلق ورقة إضافة منشور أولاً
    Navigator.push(
      context,
      MaterialPageRoute(builder: (_) => const LiveStreamSetupPage()),
    );
  }

  Future<void> _recordAndSendVoice() async {
    if (_isRecording) return;

    // طلب صلاحية مايك
    final hasPerm = await _recorder.hasPermission();
    if (!hasPerm) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('لا يوجد إذن لاستخدام الميكروفون')));
      return;
    }

    setState(() => _isRecording = true);

    final dir = await getTemporaryDirectory();
    final filePath = p.join(dir.path, 'voice_${DateTime.now().millisecondsSinceEpoch}.m4a');

    await _recorder.start(const RecordConfig(encoder: AudioEncoder.aacLc), path: filePath);

    // حوار يعرض مدة التسجيل مع إمكانية الإيقاف أو الإلغاء
    final bool? didStop = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (ctx) => _RecordingDialog(onStop: () => Navigator.pop(ctx, true), onCancel: () => Navigator.pop(ctx, false)),
    );

    final recordedPath = await _recorder.stop();
    setState(() => _isRecording = false);

    // إذا ألغى المستخدم أو لم يُنشأ الملف، لا تُتابع
    if (didStop != true || recordedPath == null) return;

    final bytes = await File(recordedPath).readAsBytes();
    if (bytes.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('فشل تسجيل الصوت')));
      return;
    }

    final fileNameUp = p.basename(recordedPath);
    final mediaUrl = await SupabaseService().uploadMedia(bytes, fileNameUp);

    await SupabaseService().createPost(
      content: '',
      type: PostType.voice,
      mediaUrl: mediaUrl,
    );

    if (mounted) {
      Navigator.pop(context); // أغلق ورقة النشر
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم نشر الرسالة الصوتية')));
    }
  }

  Future<void> _submit() async {
    if (_textController.text.trim().isEmpty && _selectedMedia == null && _linkUrl == null && _bgColor == null) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('لا يمكن نشر منشور فارغ')));
      return;
    }

    setState(() => _isLoading = true);

    String? mediaUrl;
    try {
      if (_selectedMedia != null) {
        final bytes = await _selectedMedia!.readAsBytes();
        final ext = p.extension(_selectedMedia!.path).replaceFirst('.', '');
        final filename = 'post_${DateTime.now().millisecondsSinceEpoch}.$ext';
        mediaUrl = await SupabaseService().uploadMedia(bytes, filename);
      }

      PostType finalType;
      if (_mediaType != null) {
        finalType = _mediaType!;
      } else if (_linkUrl != null) {
        finalType = PostType.link;
      } else if (_bgColor != null) {
        finalType = PostType.text;
      } else {
        finalType = PostType.text;
      }

      await SupabaseService().createPost(
        content: _textController.text.trim(),
        type: finalType,
        mediaUrl: mediaUrl,
        linkUrl: _linkUrl,
        linkMeta: _linkMeta,
        bgColor: _bgColor,
      );

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم نشر المنشور')));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('فشل النشر: $e')));
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }
}

class _ActionCircle extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;

  const _ActionCircle({
    Key? key,
    required this.icon,
    required this.label,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(
              icon,
              size: 24,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }
}

class _VoiceActionCircle extends StatelessWidget {
  final VoidCallback onSend;

  const _VoiceActionCircle({
    Key? key,
    required this.onSend,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onSend,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(
              Icons.mic,
              size: 24,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'رسالة صوتية',
            style: const TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }
}

//============= Recording Dialog ====================

class _RecordingDialog extends StatefulWidget {
  final VoidCallback onStop;
  final VoidCallback onCancel;
  const _RecordingDialog({Key? key, required this.onStop, required this.onCancel}) : super(key: key);

  @override
  State<_RecordingDialog> createState() => _RecordingDialogState();
}

class _RecordingDialogState extends State<_RecordingDialog> {
  late final Timer _timer;
  int _seconds = 0;

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      setState(() => _seconds++);
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  String _format(int totalSeconds) {
    final m = (totalSeconds ~/ 60).toString().padLeft(2, '0');
    final s = (totalSeconds % 60).toString().padLeft(2, '0');
    return '$m:$s';
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('جارى التسجيل'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.mic, size: 48, color: Colors.red),
          const SizedBox(height: 12),
          Text(_format(_seconds), style: const TextStyle(fontSize: 24)),
        ],
      ),
      actions: [
        TextButton(onPressed: widget.onCancel, child: const Text('إلغاء')),
        TextButton(onPressed: widget.onStop, child: const Text('إيقاف')),
      ],
    );
  }
} 