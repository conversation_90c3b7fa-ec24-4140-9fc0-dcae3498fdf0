import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import '../models/voice_reaction.dart';
import '../supabase_service.dart';

class VoiceReactionsSheet extends StatefulWidget {
  final String postId;

  const VoiceReactionsSheet({
    super.key,
    required this.postId,
  });

  @override
  State<VoiceReactionsSheet> createState() => _VoiceReactionsSheetState();
}

class _VoiceReactionsSheetState extends State<VoiceReactionsSheet> {
  final SupabaseService _supabaseService = SupabaseService();
  final AudioPlayer _audioPlayer = AudioPlayer();
  
  List<VoiceReaction> _voiceReactions = [];
  bool _isLoading = true;
  String? _currentPlayingId;
  bool _isPlaying = false;

  @override
  void initState() {
    super.initState();
    _loadVoiceReactions();
    
    // الاستماع لانتهاء التشغيل
    _audioPlayer.onPlayerComplete.listen((_) {
      if (mounted) {
        setState(() {
          _currentPlayingId = null;
          _isPlaying = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  Future<void> _loadVoiceReactions() async {
    try {
      final reactions = await _supabaseService.getPostVoiceReactions(widget.postId);
      if (mounted) {
        setState(() {
          _voiceReactions = reactions;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل التفاعلات الصوتية: $e')),
        );
      }
    }
  }

  Future<void> _playVoiceReaction(VoiceReaction reaction) async {
    try {
      if (_currentPlayingId == reaction.id && _isPlaying) {
        // إيقاف التشغيل الحالي
        await _audioPlayer.stop();
        setState(() {
          _currentPlayingId = null;
          _isPlaying = false;
        });
        return;
      }

      // إيقاف أي تشغيل سابق
      await _audioPlayer.stop();

      // بدء تشغيل جديد
      await _audioPlayer.play(UrlSource(reaction.audioUrl));
      
      setState(() {
        _currentPlayingId = reaction.id;
        _isPlaying = true;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('خطأ في تشغيل التسجيل الصوتي')),
        );
      }
    }
  }

  String _formatDuration(int? seconds) {
    if (seconds == null) return '00:00';
    final duration = Duration(seconds: seconds);
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes);
    final secs = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$secs';
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'قبل ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'قبل ${difference.inHours} ساعة';
    } else {
      return 'قبل ${difference.inDays} يوم';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade200),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.mic, color: Colors.red),
                const SizedBox(width: 8),
                const Text(
                  'التفاعلات الصوتية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_voiceReactions.length}',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          
          // Content
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _voiceReactions.isEmpty
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.mic_off,
                              size: 64,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'لا توجد تفاعلات صوتية بعد',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _voiceReactions.length,
                        itemBuilder: (context, index) {
                          final reaction = _voiceReactions[index];
                          final isPlaying = _currentPlayingId == reaction.id && _isPlaying;
                          
                          return Container(
                            margin: const EdgeInsets.only(bottom: 12),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade50,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: isPlaying ? Colors.red : Colors.grey.shade200,
                                width: isPlaying ? 2 : 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                // Avatar placeholder (يمكن إضافة صورة المستخدم لاحقاً)
                                CircleAvatar(
                                  radius: 20,
                                  backgroundColor: Colors.grey.shade300,
                                  child: const Icon(Icons.person, color: Colors.white),
                                ),
                                const SizedBox(width: 12),
                                
                                // User info and time
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'مستخدم', // يمكن إضافة اسم المستخدم لاحقاً
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 14,
                                        ),
                                      ),
                                      Text(
                                        _formatTime(reaction.createdAt),
                                        style: TextStyle(
                                          color: Colors.grey.shade600,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                
                                // Duration
                                Text(
                                  _formatDuration(reaction.audioDuration),
                                  style: TextStyle(
                                    color: Colors.grey.shade600,
                                    fontSize: 12,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                
                                // Play button
                                GestureDetector(
                                  onTap: () => _playVoiceReaction(reaction),
                                  child: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: isPlaying ? Colors.red : Colors.blue,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      isPlaying ? Icons.pause : Icons.play_arrow,
                                      color: Colors.white,
                                      size: 20,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }
}
