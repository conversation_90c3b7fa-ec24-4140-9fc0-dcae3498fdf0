import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../models/post.dart';
import '../models/comment.dart';
import '../models/reaction_type.dart';
import '../supabase_service.dart';
import 'dart:io';

class CommentsSheet extends StatefulWidget {
  final Post post;
  final VoidCallback? onCommentAdded;

  const CommentsSheet({
    super.key,
    required this.post,
    this.onCommentAdded,
  });

  @override
  State<CommentsSheet> createState() => _CommentsSheetState();
}

class _CommentsSheetState extends State<CommentsSheet> {
  final TextEditingController _commentController = TextEditingController();
  String? _selectedMediaPath;
  CommentType _selectedMediaType = CommentType.text;
  late Stream<List<Comment>> _stream;
  final ImagePicker _picker = ImagePicker();
  final ScrollController _scrollController = ScrollController();
  bool _sending = false;

  @override
  void initState() {
    super.initState();
    _stream = SupabaseService().commentsStream(widget.post.id);
  }

  @override
  void dispose() {
    _commentController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _pickMedia(bool isVideo) async {
    final XFile? file = isVideo
        ? await _picker.pickVideo(source: ImageSource.gallery)
        : await _picker.pickImage(source: ImageSource.gallery);

    if (file != null) {
      setState(() {
        _selectedMediaPath = file.path;
        _selectedMediaType = isVideo ? CommentType.video : CommentType.image;
      });
    }
  }

  void _submitComment() {
    if (_commentController.text.isNotEmpty || _selectedMediaPath != null) {
      _addComment();
    }
  }

  Future<void> _addComment() async {
    if (_sending) return;
    setState(() => _sending = true);
    String? mediaUrl;

    if (_selectedMediaPath != null) {
      final bytes = await File(_selectedMediaPath!).readAsBytes();
      final ext = _selectedMediaPath!.split('.').last;
      final storagePath = 'comments/${widget.post.id}/${DateTime.now().millisecondsSinceEpoch}.$ext';
      mediaUrl = await SupabaseService().uploadMedia(bytes, storagePath);
    }

    try {
      await SupabaseService().createComment(
        postId: widget.post.id,
        content: _commentController.text.trim().isEmpty && _selectedMediaPath != null
            ? '(ميديا)'
            : _commentController.text.trim(),
        type: _selectedMediaType,
        mediaUrl: mediaUrl,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل نشر التعليق: $e')),
        );
      }
    }

    _commentController.clear();
    setState(() {
      _selectedMediaPath = null;
      _selectedMediaType = CommentType.text;
    });

    // Stream سيجلب التعليق الجديد تلقائياً

    if (mounted) setState(() => _sending = false);

    if (widget.onCommentAdded != null) {
      widget.onCommentAdded!();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          Expanded(
            child: StreamBuilder<List<Comment>>(
              stream: _stream,
              builder: (context, snapshot) {
                if (!snapshot.hasData) {
                  return const Center(child: CircularProgressIndicator());
                }
                final comments = snapshot.data!;
                if (comments.isEmpty) {
                  return const Center(child: Text('لا توجد تعليقات'));
                }
                return ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(8),
                  itemCount: comments.length,
                  itemBuilder: (context, index) {
                    final comment = comments[index];
                    return Card(
                      margin: const EdgeInsets.symmetric(vertical: 4),
                      child: Padding(
                        padding: const EdgeInsets.all(8),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                CircleAvatar(
                                  backgroundColor: Colors.red[300],
                                  child: Text(comment.userName[0]),
                                ),
                                const SizedBox(width: 8),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      comment.userName,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      'منذ ${DateTime.now().difference(comment.createdAt).inMinutes} دقيقة',
                                      style: TextStyle(
                                        color: Colors.grey[600],
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(comment.content),
                            if (comment.mediaUrl != null) ...[
                              const SizedBox(height: 8),
                              if (comment.type == CommentType.image)
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image.network(
                                    comment.mediaUrl!,
                                    fit: BoxFit.cover,
                                  ),
                                )
                              else
                                Container(
                                  height: 200,
                                  decoration: BoxDecoration(
                                    color: Colors.black,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: const Center(
                                    child: Icon(
                                      Icons.play_circle_outline,
                                      color: Colors.white,
                                      size: 50,
                                    ),
                                  ),
                                ),
                            ],
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                IconButton(
                                  icon: Icon(
                                    comment.currentUserReaction == ReactionType.like
                                        ? Icons.thumb_up
                                        : Icons.thumb_up_outlined,
                                    size: 16,
                                    color: comment.currentUserReaction == ReactionType.like
                                        ? Colors.blue
                                        : Colors.grey,
                                  ),
                                  onPressed: () {},
                                  constraints: const BoxConstraints(),
                                  padding: EdgeInsets.zero,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${comment.likesCount}',
                                  style: const TextStyle(fontSize: 12),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
          if (_selectedMediaPath != null)
            Container(
              height: 100,
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(10),
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  if (_selectedMediaType == CommentType.image)
                    Image.file(
                      File(_selectedMediaPath!),
                      fit: BoxFit.cover,
                    )
                  else
                    const Icon(Icons.video_file, size: 50),
                  Positioned(
                    top: 4,
                    right: 4,
                    child: IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () {
                        setState(() {
                          _selectedMediaPath = null;
                          _selectedMediaType = CommentType.text;
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
          SafeArea(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                  ),
                ],
              ),
              child: Row(
                children: [
                  const CircleAvatar(
                    backgroundColor: Colors.red,
                    child: Text('م'),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: _commentController,
                      decoration: InputDecoration(
                        hintText: 'اكتب تعليقاً...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(20),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Colors.grey[100],
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                      ),
                      maxLines: null,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.image, color: Colors.red),
                    onPressed: () => _pickMedia(false),
                  ),
                  IconButton(
                    icon: const Icon(Icons.videocam, color: Colors.red),
                    onPressed: () => _pickMedia(true),
                  ),
                  IconButton(
                    icon: const Icon(Icons.send, color: Colors.red),
                    onPressed: _sending ? null : _submitComment,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}