import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import '../services/voice_recording_service.dart';
import '../supabase_service.dart';
import '../models/voice_reaction.dart';

class VoiceReactionWidget extends StatefulWidget {
  final String postId;
  final VoidCallback? onReactionAdded;

  const VoiceReactionWidget({
    super.key,
    required this.postId,
    this.onReactionAdded,
  });

  @override
  State<VoiceReactionWidget> createState() => _VoiceReactionWidgetState();
}

class _VoiceReactionWidgetState extends State<VoiceReactionWidget>
    with TickerProviderStateMixin {
  final VoiceRecordingService _recordingService = VoiceRecordingService();
  final SupabaseService _supabaseService = SupabaseService();
  final AudioPlayer _audioPlayer = AudioPlayer();

  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  
  bool _isRecording = false;
  bool _isUploading = false;
  bool _isPlaying = false;
  Duration _recordingDuration = Duration.zero;
  VoiceReaction? _currentVoiceReaction;
  String? _recordedFilePath;

  @override
  void initState() {
    super.initState();
    
    // تهيئة التأثيرات المتحركة
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // الاستماع لتغييرات التسجيل
    _recordingService.isRecordingNotifier.addListener(_onRecordingStateChanged);
    _recordingService.recordingDurationNotifier.addListener(_onDurationChanged);

    // تحميل التفاعل الصوتي الحالي
    _loadCurrentVoiceReaction();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _audioPlayer.dispose();
    _recordingService.isRecordingNotifier.removeListener(_onRecordingStateChanged);
    _recordingService.recordingDurationNotifier.removeListener(_onDurationChanged);
    super.dispose();
  }

  void _onRecordingStateChanged() {
    if (mounted) {
      setState(() {
        _isRecording = _recordingService.isRecording;
      });
      
      if (_isRecording) {
        _pulseController.repeat(reverse: true);
      } else {
        _pulseController.stop();
        _pulseController.reset();
      }
    }
  }

  void _onDurationChanged() {
    if (mounted) {
      setState(() {
        _recordingDuration = _recordingService.recordingDuration;
      });
    }
  }

  Future<void> _loadCurrentVoiceReaction() async {
    final reaction = await _supabaseService.getUserVoiceReaction(widget.postId);
    if (mounted) {
      setState(() {
        _currentVoiceReaction = reaction;
      });
    }
  }

  Future<void> _startRecording() async {
    final success = await _recordingService.startRecording();
    if (!success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('فشل في بدء التسجيل. تحقق من إذن الميكروفون.')),
      );
    }
  }

  Future<void> _stopRecording() async {
    final filePath = await _recordingService.stopRecording();
    if (filePath != null) {
      setState(() {
        _recordedFilePath = filePath;
      });
      await _uploadVoiceReaction(filePath);
    }
  }

  Future<void> _uploadVoiceReaction(String filePath) async {
    setState(() {
      _isUploading = true;
    });

    try {
      // رفع الملف
      final audioUrl = await _supabaseService.uploadVoiceReaction(filePath, widget.postId);
      if (audioUrl == null) throw Exception('فشل في رفع الملف');

      // الحصول على مدة التسجيل
      final duration = await _recordingService.getAudioDuration(filePath);

      // إنشاء التفاعل الصوتي
      final voiceReaction = await _supabaseService.createVoiceReaction(
        postId: widget.postId,
        audioUrl: audioUrl,
        audioDuration: duration?.inSeconds,
      );

      if (voiceReaction != null) {
        setState(() {
          _currentVoiceReaction = voiceReaction;
        });
        
        if (widget.onReactionAdded != null) {
          widget.onReactionAdded!();
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم إضافة التفاعل الصوتي بنجاح!')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في إضافة التفاعل الصوتي: $e')),
        );
      }
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  Future<void> _playVoiceReaction() async {
    if (_currentVoiceReaction == null) return;

    try {
      setState(() {
        _isPlaying = true;
      });

      await _audioPlayer.play(UrlSource(_currentVoiceReaction!.audioUrl));
      
      _audioPlayer.onPlayerComplete.listen((_) {
        if (mounted) {
          setState(() {
            _isPlaying = false;
          });
        }
      });
    } catch (e) {
      setState(() {
        _isPlaying = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('خطأ في تشغيل التسجيل')),
        );
      }
    }
  }

  Future<void> _deleteVoiceReaction() async {
    final success = await _supabaseService.deleteVoiceReaction(widget.postId);
    if (success) {
      setState(() {
        _currentVoiceReaction = null;
      });
      
      if (widget.onReactionAdded != null) {
        widget.onReactionAdded!();
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حذف التفاعل الصوتي')),
        );
      }
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes);
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    if (_currentVoiceReaction != null) {
      // عرض التفاعل الصوتي الموجود
      return _buildExistingVoiceReaction();
    }

    if (_isRecording) {
      // عرض واجهة التسجيل
      return _buildRecordingInterface();
    }

    // عرض زر بدء التسجيل
    return _buildStartRecordingButton();
  }

  Widget _buildStartRecordingButton() {
    return GestureDetector(
      onTap: _startRecording,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: const Icon(
          Icons.mic,
          color: Colors.red,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildRecordingInterface() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // زر الإلغاء
          GestureDetector(
            onTap: () async {
              await _recordingService.cancelRecording();
            },
            child: const Icon(Icons.close, color: Colors.red, size: 20),
          ),
          const SizedBox(width: 8),
          
          // أيقونة الميكروفون المتحركة
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: const Icon(Icons.mic, color: Colors.red, size: 20),
              );
            },
          ),
          const SizedBox(width: 8),
          
          // مدة التسجيل
          Text(
            _formatDuration(_recordingDuration),
            style: const TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 8),
          
          // زر الإيقاف
          GestureDetector(
            onTap: _stopRecording,
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.stop, color: Colors.white, size: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExistingVoiceReaction() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // زر التشغيل/الإيقاف
          GestureDetector(
            onTap: _playVoiceReaction,
            child: Icon(
              _isPlaying ? Icons.pause : Icons.play_arrow,
              color: Colors.blue,
              size: 20,
            ),
          ),
          const SizedBox(width: 8),
          
          // مدة التسجيل
          if (_currentVoiceReaction!.audioDuration != null)
            Text(
              _formatDuration(Duration(seconds: _currentVoiceReaction!.audioDuration!)),
              style: const TextStyle(
                color: Colors.blue,
                fontSize: 12,
              ),
            ),
          const SizedBox(width: 8),
          
          // زر الحذف
          GestureDetector(
            onTap: _deleteVoiceReaction,
            child: const Icon(Icons.delete, color: Colors.red, size: 16),
          ),
        ],
      ),
    );
  }
}
